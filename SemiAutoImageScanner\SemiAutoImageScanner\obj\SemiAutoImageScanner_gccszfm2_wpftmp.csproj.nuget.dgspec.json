{"format": 1, "restore": {"D:\\办公用\\公司软件\\AIDFI-1.5代机\\SemiAutoImageScanner\\SemiAutoImageScanner\\SemiAutoImageScanner.csproj": {}}, "projects": {"D:\\办公用\\公司软件\\AIDFI-1.5代机\\SemiAutoImageScanner\\SemiAutoImageScanner\\SemiAutoImageScanner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\办公用\\公司软件\\AIDFI-1.5代机\\SemiAutoImageScanner\\SemiAutoImageScanner\\SemiAutoImageScanner.csproj", "projectName": "SemiAutoImageScanner", "projectPath": "D:\\办公用\\公司软件\\AIDFI-1.5代机\\SemiAutoImageScanner\\SemiAutoImageScanner\\SemiAutoImageScanner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\办公用\\公司软件\\AIDFI-1.5代机\\SemiAutoImageScanner\\SemiAutoImageScanner\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\soft\\DevExpress 21.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 21.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "D:\\soft\\DevExpress 21.2\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "NModbus4": {"target": "Package", "version": "[2.1.0, )"}, "Serilog": {"target": "Package", "version": "[4.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[5.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}, "System.Drawing.Common": {"target": "Package", "version": "[8.0.0, )"}, "System.IO.Ports": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102/PortableRuntimeIdentifierGraph.json"}}}}}