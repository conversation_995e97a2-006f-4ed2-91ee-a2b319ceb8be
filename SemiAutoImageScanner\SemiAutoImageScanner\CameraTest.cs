using System;
using System.Threading.Tasks;
using SemiAutoImageScanner.Services;
using Serilog;

namespace SemiAutoImageScanner
{
    /// <summary>
    /// 相机测试程序
    /// </summary>
    public class CameraTest
    {
        private readonly ILogger _logger;
        private readonly DahengCameraService _cameraService;

        public CameraTest()
        {
            // 配置日志
            _logger = new LoggerConfiguration()
                .WriteTo.Console()
                .WriteTo.File("logs/camera-test-.log", rollingInterval: RollingInterval.Day)
                .CreateLogger();

            _cameraService = new DahengCameraService(_logger);
        }

        /// <summary>
        /// 运行相机测试
        /// </summary>
        public async Task RunTestAsync()
        {
            try
            {
                _logger.Information("=== 开始相机测试 ===");

                // 1. 测试相机初始化
                _logger.Information("1. 测试相机初始化...");
                var initResult = await _cameraService.InitializeAsync();
                _logger.Information("相机初始化结果: {Result}", initResult ? "成功" : "失败");

                if (!initResult)
                {
                    _logger.Warning("相机初始化失败，可能没有连接真实相机设备");
                    return;
                }

                // 2. 获取相机信息
                _logger.Information("2. 获取相机信息...");
                var cameraInfo = _cameraService.CameraInfo;
                if (cameraInfo != null)
                {
                    _logger.Information("相机信息:");
                    _logger.Information("  名称: {Name}", cameraInfo.Name);
                    _logger.Information("  序列号: {SerialNumber}", cameraInfo.SerialNumber);
                    _logger.Information("  型号: {Model}", cameraInfo.Model);
                    _logger.Information("  厂商: {Vendor}", cameraInfo.Vendor);
                    _logger.Information("  版本: {Version}", cameraInfo.Version);
                    _logger.Information("  连接状态: {IsConnected}", cameraInfo.IsConnected);
                }

                // 3. 测试获取可用相机列表
                _logger.Information("3. 测试获取可用相机列表...");
                var cameras = await _cameraService.GetAvailableCamerasAsync();
                _logger.Information("找到 {Count} 个相机设备", cameras.Length);
                foreach (var camera in cameras)
                {
                    _logger.Information("  相机 {Index}: {Name} ({SerialNumber})", camera.Index, camera.Name, camera.SerialNumber);
                }

                // 4. 测试相机参数设置
                _logger.Information("4. 测试相机参数设置...");
                var parameters = _cameraService.Parameters;
                if (parameters != null)
                {
                    _logger.Information("当前相机参数:");
                    _logger.Information("  曝光时间: {ExposureTime}", parameters.ExposureTime);
                    _logger.Information("  增益: {Gain}", parameters.Gain);
                    _logger.Information("  白平衡: {WhiteBalance}", parameters.WhiteBalanceRatio);
                    _logger.Information("  分辨率: {Width}x{Height}", parameters.Width, parameters.Height);
                    _logger.Information("  像素格式: {PixelFormat}", parameters.PixelFormat);
                }

                // 5. 测试拍照功能
                _logger.Information("5. 测试拍照功能...");
                var imageData = await _cameraService.CaptureImageAsync();
                if (imageData != null && imageData.Length > 0)
                {
                    _logger.Information("拍照成功，图像大小: {Size} bytes", imageData.Length);
                    
                    // 保存测试图片
                    var testImagePath = $"test-image-{DateTime.Now:yyyyMMdd-HHmmss}.bmp";
                    await System.IO.File.WriteAllBytesAsync(testImagePath, imageData);
                    _logger.Information("测试图片已保存: {Path}", testImagePath);
                }
                else
                {
                    _logger.Warning("拍照失败或返回空数据");
                }

                // 6. 测试预览功能
                _logger.Information("6. 测试预览功能...");
                
                // 注册预览事件
                int frameCount = 0;
                _cameraService.PreviewFrameReceived += (sender, e) =>
                {
                    frameCount++;
                    if (frameCount % 10 == 0) // 每10帧输出一次
                    {
                        _logger.Information("收到预览帧 #{Count}, 尺寸: {Width}x{Height}", frameCount, e.Frame.PixelWidth, e.Frame.PixelHeight);
                    }
                };

                // 开始预览
                var previewResult = await _cameraService.StartPreviewAsync();
                _logger.Information("开始预览结果: {Result}", previewResult ? "成功" : "失败");

                if (previewResult)
                {
                    // 预览5秒
                    _logger.Information("预览5秒...");
                    await Task.Delay(5000);

                    // 停止预览
                    var stopResult = await _cameraService.StopPreviewAsync();
                    _logger.Information("停止预览结果: {Result}", stopResult ? "成功" : "失败");
                    _logger.Information("总共收到 {Count} 帧预览图像", frameCount);
                }

                // 7. 关闭相机
                _logger.Information("7. 关闭相机...");
                await _cameraService.CloseAsync();
                _logger.Information("相机已关闭");

                _logger.Information("=== 相机测试完成 ===");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "相机测试过程中发生错误");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _cameraService?.Dispose();
        }
    }
}
