﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 2013
VisualStudioVersion = 12.0.21005.1
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GxCameraEvents_VS2013", "GxCameraEvents\GxCameraEvents_VS2013.csproj", "{5C760823-8903-4FDF-9556-02FE8E8A0F19}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GxGetImage_VS2013", "GxGetImage\GxGetImage_VS2013.csproj", "{2CA0FAE8-78E2-4CD2-8A97-43657C8365A5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GxGigeRecovery_VS2013", "GxGigeRecovery\GxGigeRecovery_VS2013.csproj", "{8AB91E09-E09B-493D-97C5-7988E64DC519}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GxImageProcess_VS2013", "GxImageProcess\GxImageProcess_VS2013.csproj", "{7D72503C-8CC4-46B3-B976-75BFACDD00A0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GxMultiCam_VS2013", "GxMultiCam\GxMultiCam_VS2013.csproj", "{B5A7E253-F7CE-4462-84D2-9BAA32275C65}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GxSingleCam_VS2013", "GxSingleCam\GxSingleCam_VS2013.csproj", "{4D55AE12-A9D6-4EF9-A0AB-347975C65F59}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GxMultiCamForHDR_VS2013", "GxMultiCamForHDR\GxMultiCamForHDR_VS2013.csproj", "{8297D100-70E8-4A34-9250-B1613C0863A0}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x86 = Debug|x86
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{5C760823-8903-4FDF-9556-02FE8E8A0F19}.Debug|x86.ActiveCfg = Debug|x86
		{5C760823-8903-4FDF-9556-02FE8E8A0F19}.Debug|x86.Build.0 = Debug|x86
		{5C760823-8903-4FDF-9556-02FE8E8A0F19}.Release|x86.ActiveCfg = Release|x86
		{5C760823-8903-4FDF-9556-02FE8E8A0F19}.Release|x86.Build.0 = Release|x86
		{2CA0FAE8-78E2-4CD2-8A97-43657C8365A5}.Debug|x86.ActiveCfg = Debug|x86
		{2CA0FAE8-78E2-4CD2-8A97-43657C8365A5}.Debug|x86.Build.0 = Debug|x86
		{2CA0FAE8-78E2-4CD2-8A97-43657C8365A5}.Release|x86.ActiveCfg = Release|x86
		{2CA0FAE8-78E2-4CD2-8A97-43657C8365A5}.Release|x86.Build.0 = Release|x86
		{8AB91E09-E09B-493D-97C5-7988E64DC519}.Debug|x86.ActiveCfg = Debug|x86
		{8AB91E09-E09B-493D-97C5-7988E64DC519}.Debug|x86.Build.0 = Debug|x86
		{8AB91E09-E09B-493D-97C5-7988E64DC519}.Release|x86.ActiveCfg = Release|x86
		{8AB91E09-E09B-493D-97C5-7988E64DC519}.Release|x86.Build.0 = Release|x86
		{7D72503C-8CC4-46B3-B976-75BFACDD00A0}.Debug|x86.ActiveCfg = Debug|x86
		{7D72503C-8CC4-46B3-B976-75BFACDD00A0}.Debug|x86.Build.0 = Debug|x86
		{7D72503C-8CC4-46B3-B976-75BFACDD00A0}.Release|x86.ActiveCfg = Release|x86
		{7D72503C-8CC4-46B3-B976-75BFACDD00A0}.Release|x86.Build.0 = Release|x86
		{B5A7E253-F7CE-4462-84D2-9BAA32275C65}.Debug|x86.ActiveCfg = Debug|x86
		{B5A7E253-F7CE-4462-84D2-9BAA32275C65}.Debug|x86.Build.0 = Debug|x86
		{B5A7E253-F7CE-4462-84D2-9BAA32275C65}.Release|x86.ActiveCfg = Release|x86
		{B5A7E253-F7CE-4462-84D2-9BAA32275C65}.Release|x86.Build.0 = Release|x86
		{4D55AE12-A9D6-4EF9-A0AB-347975C65F59}.Debug|x86.ActiveCfg = Debug|x86
		{4D55AE12-A9D6-4EF9-A0AB-347975C65F59}.Debug|x86.Build.0 = Debug|x86
		{4D55AE12-A9D6-4EF9-A0AB-347975C65F59}.Release|x86.ActiveCfg = Release|x86
		{4D55AE12-A9D6-4EF9-A0AB-347975C65F59}.Release|x86.Build.0 = Release|x86
		{8297D100-70E8-4A34-9250-B1613C0863A0}.Debug|x86.ActiveCfg = Debug|x86
		{8297D100-70E8-4A34-9250-B1613C0863A0}.Debug|x86.Build.0 = Debug|x86
		{8297D100-70E8-4A34-9250-B1613C0863A0}.Release|x86.ActiveCfg = Release|x86
		{8297D100-70E8-4A34-9250-B1613C0863A0}.Release|x86.Build.0 = Release|x86
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
