2025-08-05 12:31:59.853 +08:00 [INF] 半自动影像扫描系统启动
2025-08-05 12:32:14.821 +08:00 [INF] Modbus TCP连接已断开
2025-08-05 12:32:19.543 +08:00 [INF] Modbus TCP连接已断开
2025-08-05 12:32:19.548 +08:00 [ERR] Modbus TCP连接失败: *************:502
System.Net.Sockets.SocketException (995): 由于线程退出或应用程序请求，已中止 I/O 操作。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Threading.Tasks.ValueTask.ValueTaskSourceAsTask.<>c.<.cctor>b__4_0(Object state)
--- End of stack trace from previous location ---
   at System.Net.Sockets.TcpClient.CompleteConnectAsync(Task task)
   at SemiAutoImageScanner.Services.ModbusTcpService.ConnectAsync(String ipAddress, Int32 port, Byte slaveId) in D:\办公用\公司软件\AIDFI-1.5代机\SemiAutoImageScanner\SemiAutoImageScanner\Services\ModbusTcpService.cs:line 50
2025-08-05 12:32:25.544 +08:00 [INF] Modbus TCP连接已断开
2025-08-05 12:32:25.544 +08:00 [INF] 应用程序关闭
2025-08-05 12:32:25.554 +08:00 [ERR] Modbus TCP连接失败: *************:502
System.Net.Sockets.SocketException (995): 由于线程退出或应用程序请求，已中止 I/O 操作。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Threading.Tasks.ValueTask.ValueTaskSourceAsTask.<>c.<.cctor>b__4_0(Object state)
--- End of stack trace from previous location ---
   at System.Net.Sockets.TcpClient.CompleteConnectAsync(Task task)
   at SemiAutoImageScanner.Services.ModbusTcpService.ConnectAsync(String ipAddress, Int32 port, Byte slaveId) in D:\办公用\公司软件\AIDFI-1.5代机\SemiAutoImageScanner\SemiAutoImageScanner\Services\ModbusTcpService.cs:line 50
2025-08-05 12:42:02.119 +08:00 [INF] 半自动影像扫描系统启动
2025-08-05 13:01:46.500 +08:00 [INF] Modbus TCP连接已断开
2025-08-05 13:01:46.501 +08:00 [INF] 应用程序关闭
2025-08-05 13:02:07.893 +08:00 [INF] 半自动影像扫描系统启动
2025-08-05 13:06:23.972 +08:00 [INF] 图片管理服务初始化完成，共加载 0 张图片
2025-08-05 13:06:24.003 +08:00 [INF] 自动扫描页面初始化完成
2025-08-05 13:06:24.004 +08:00 [INF] 半自动影像扫描系统启动
2025-08-05 13:06:44.597 +08:00 [INF] 参数设置: 参数加载完成
2025-08-05 13:06:44.597 +08:00 [INF] 参数设置页面初始化完成
2025-08-05 13:06:48.098 +08:00 [INF] 图片管理服务初始化完成，共加载 0 张图片
2025-08-05 13:06:48.099 +08:00 [INF] 手动控制: 手动控制页面初始化完成
2025-08-05 13:06:54.299 +08:00 [INF] 手动控制: 开始手动拍照...
2025-08-05 13:06:54.300 +08:00 [INF] 开始图片采集
2025-08-05 13:06:54.301 +08:00 [INF] 相机未初始化，生成模拟图片
2025-08-05 13:06:54.412 +08:00 [INF] 图片已添加: Manual_20250805_130654_301.jpg
2025-08-05 13:06:54.412 +08:00 [INF] 图片采集完成: Manual_20250805_130654_301.jpg
2025-08-05 13:06:54.414 +08:00 [ERR] 手动拍照失败
System.Windows.Markup.XamlParseException: 不能在“Binding”类型的“TargetNullValue”属性上设置“Binding”。只能在 DependencyObject 的 DependencyProperty 上设置“Binding”。
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, Uri baseUri)
   at System.Windows.FrameworkTemplate.LoadTemplateXaml(XamlReader templateReader, XamlObjectWriter currentWriter)
   at System.Windows.FrameworkTemplate.LoadOptimizedTemplateContent(DependencyObject container, IComponentConnector componentConnector, IStyleConnector styleConnector, List`1 affectedChildren, UncommonField`1 templatedNonFeChildrenField)
   at System.Windows.FrameworkTemplate.LoadContent(DependencyObject container, List`1 affectedChildren)
   at System.Windows.StyleHelper.ApplyTemplateContent(UncommonField`1 dataField, DependencyObject container, FrameworkElementFactory templateRoot, Int32 lastChildIndex, HybridDictionary childIndexFromChildID, FrameworkTemplate frameworkTemplate)
   at System.Windows.FrameworkTemplate.ApplyTemplateContent(UncommonField`1 templateDataField, FrameworkElement container)
   at System.Windows.FrameworkElement.ApplyTemplate()
   at System.Windows.FrameworkElement.MeasureCore(Size availableSize)
   at System.Windows.UIElement.Measure(Size availableSize)
   at System.Windows.Controls.Border.MeasureOverride(Size constraint)
   at System.Windows.FrameworkElement.MeasureCore(Size availableSize)
   at System.Windows.UIElement.Measure(Size availableSize)
   at System.Windows.Controls.Control.MeasureOverride(Size constraint)
   at System.Windows.FrameworkElement.MeasureCore(Size availableSize)
   at System.Windows.UIElement.Measure(Size availableSize)
   at System.Windows.Controls.WrapPanel.MeasureOverride(Size constraint)
   at System.Windows.FrameworkElement.MeasureCore(Size availableSize)
   at System.Windows.UIElement.Measure(Size availableSize)
   at System.Windows.ContextLayoutManager.UpdateLayout()
   at System.Windows.Controls.Primitives.TextBoxBase.ScrollToEnd()
   at SemiAutoImageScanner.Views.ManualControlPage.<>c__DisplayClass31_0.<LogMessage>b__0() in D:\办公用\公司软件\AIDFI-1.5代机\SemiAutoImageScanner\SemiAutoImageScanner\Views\ManualControlPage.xaml.cs:line 402
   at System.Windows.Threading.Dispatcher.Invoke(Action callback, DispatcherPriority priority, CancellationToken cancellationToken, TimeSpan timeout)
   at System.Windows.Threading.Dispatcher.Invoke(Action callback)
   at SemiAutoImageScanner.Views.ManualControlPage.LogMessage(String message) in D:\办公用\公司软件\AIDFI-1.5代机\SemiAutoImageScanner\SemiAutoImageScanner\Views\ManualControlPage.xaml.cs:line 399
   at SemiAutoImageScanner.Views.ManualControlPage.ManualCaptureButton_Click(Object sender, RoutedEventArgs e) in D:\办公用\公司软件\AIDFI-1.5代机\SemiAutoImageScanner\SemiAutoImageScanner\Views\ManualControlPage.xaml.cs:line 162
2025-08-05 13:08:26.653 +08:00 [INF] 图片管理服务初始化完成，共加载 0 张图片
2025-08-05 13:08:26.720 +08:00 [INF] 自动扫描页面初始化完成
2025-08-05 13:08:26.721 +08:00 [INF] 半自动影像扫描系统启动
2025-08-05 13:11:47.515 +08:00 [INF] Modbus TCP连接已断开
2025-08-05 13:12:05.862 +08:00 [INF] 图片管理服务初始化完成，共加载 0 张图片
2025-08-05 13:12:05.863 +08:00 [INF] 手动控制: 手动控制页面初始化完成
2025-08-05 13:12:08.559 +08:00 [ERR] Modbus TCP连接失败: *************:502
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Threading.Tasks.ValueTask.ValueTaskSourceAsTask.<>c.<.cctor>b__4_0(Object state)
--- End of stack trace from previous location ---
   at System.Net.Sockets.TcpClient.CompleteConnectAsync(Task task)
   at SemiAutoImageScanner.Services.ModbusTcpService.ConnectAsync(String ipAddress, Int32 port, Byte slaveId) in D:\办公用\公司软件\AIDFI-1.5代机\SemiAutoImageScanner\SemiAutoImageScanner\Services\ModbusTcpService.cs:line 50
2025-08-05 13:12:21.876 +08:00 [INF] 参数设置: 参数加载完成
2025-08-05 13:12:21.876 +08:00 [INF] 参数设置页面初始化完成
2025-08-05 13:12:24.782 +08:00 [INF] 参数设置: 正在测试连接到 *************:502...
2025-08-05 13:12:24.783 +08:00 [INF] Modbus TCP连接已断开
2025-08-05 13:12:45.789 +08:00 [ERR] Modbus TCP连接失败: *************:502
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Threading.Tasks.ValueTask.ValueTaskSourceAsTask.<>c.<.cctor>b__4_0(Object state)
--- End of stack trace from previous location ---
   at System.Net.Sockets.TcpClient.CompleteConnectAsync(Task task)
   at SemiAutoImageScanner.Services.ModbusTcpService.ConnectAsync(String ipAddress, Int32 port, Byte slaveId) in D:\办公用\公司软件\AIDFI-1.5代机\SemiAutoImageScanner\SemiAutoImageScanner\Services\ModbusTcpService.cs:line 50
2025-08-05 13:12:45.794 +08:00 [INF] 参数设置: 连接测试失败！
2025-08-05 13:13:12.299 +08:00 [INF] 参数设置: 图片参数应用成功
2025-08-05 13:13:32.403 +08:00 [INF] 已选择所有图片
2025-08-05 13:13:34.648 +08:00 [INF] 已选择所有图片
2025-08-05 13:13:36.809 +08:00 [INF] 清空图片完成，删除了 0 张图片
2025-08-05 13:13:36.811 +08:00 [INF] 已清空所有图片
2025-08-05 13:13:59.150 +08:00 [INF] Modbus TCP连接已断开
2025-08-05 13:13:59.150 +08:00 [INF] 应用程序关闭
2025-08-05 13:14:16.400 +08:00 [INF] 图片管理服务初始化完成，共加载 0 张图片
2025-08-05 13:14:16.424 +08:00 [INF] 自动扫描页面初始化完成
2025-08-05 13:14:16.424 +08:00 [INF] 半自动影像扫描系统启动
2025-08-05 13:14:29.902 +08:00 [INF] 寄存器测试: 寄存器测试页面初始化完成
2025-08-05 13:15:32.263 +08:00 [INF] Modbus TCP连接已断开
2025-08-05 13:15:32.263 +08:00 [INF] 应用程序关闭
2025-08-05 13:19:10.118 +08:00 [INF] 图片管理服务初始化完成，共加载 0 张图片
2025-08-05 13:19:10.147 +08:00 [INF] 自动扫描页面初始化完成
2025-08-05 13:19:10.148 +08:00 [INF] 半自动影像扫描系统启动
2025-08-05 13:19:12.426 +08:00 [INF] 图片管理服务初始化完成，共加载 0 张图片
2025-08-05 13:19:12.426 +08:00 [INF] 手动控制: 手动控制页面初始化完成
2025-08-05 13:22:23.863 +08:00 [INF] 参数设置: 参数加载完成
2025-08-05 13:22:23.863 +08:00 [INF] 参数设置页面初始化完成
2025-08-05 13:22:25.367 +08:00 [INF] 寄存器测试: 寄存器测试页面初始化完成
2025-08-05 13:22:39.394 +08:00 [INF] Modbus TCP连接已断开
2025-08-05 13:22:39.394 +08:00 [INF] 应用程序关闭
2025-08-05 13:24:14.207 +08:00 [INF] 图片管理服务初始化完成，共加载 0 张图片
2025-08-05 13:24:14.244 +08:00 [INF] 自动扫描页面初始化完成
2025-08-05 13:24:14.245 +08:00 [INF] 半自动影像扫描系统启动
2025-08-05 13:24:16.884 +08:00 [INF] 图片管理服务初始化完成，共加载 0 张图片
2025-08-05 13:24:16.885 +08:00 [INF] 手动控制: 手动控制页面初始化完成
2025-08-05 13:24:18.211 +08:00 [INF] 参数设置: 参数加载完成
2025-08-05 13:24:18.211 +08:00 [INF] 参数设置页面初始化完成
2025-08-05 13:24:18.608 +08:00 [INF] 寄存器测试: 寄存器测试页面初始化完成
2025-08-05 13:24:23.121 +08:00 [INF] 发送设备初始化命令
2025-08-05 13:24:23.124 +08:00 [WRN] Modbus TCP未连接，无法写入线圈
2025-08-05 13:28:04.140 +08:00 [INF] Modbus TCP连接已断开
2025-08-05 13:28:04.140 +08:00 [INF] 应用程序关闭
2025-08-05 13:56:04.827 +08:00 [INF] 创建缩略图目录: Images\thumbnails
2025-08-05 13:56:04.856 +08:00 [INF] 图片管理服务初始化完成，共加载 0 张图片
2025-08-05 13:56:04.861 +08:00 [INF] 自动扫描页面初始化完成
2025-08-05 13:56:04.861 +08:00 [INF] 半自动影像扫描系统启动
2025-08-05 13:56:08.117 +08:00 [INF] Modbus TCP连接已断开
2025-08-05 13:56:08.117 +08:00 [INF] 应用程序关闭
2025-08-05 14:10:10.757 +08:00 [INF] 图片管理服务初始化完成，共加载 0 张图片
2025-08-05 14:10:10.784 +08:00 [INF] 自动扫描页面初始化完成
2025-08-05 14:10:10.785 +08:00 [INF] 半自动影像扫描系统启动
2025-08-05 14:10:15.624 +08:00 [INF] Modbus TCP连接已断开
2025-08-05 14:10:15.624 +08:00 [INF] 应用程序关闭
2025-08-05 14:27:06.631 +08:00 [INF] 图片管理服务初始化完成，共加载 0 张图片
2025-08-05 14:27:06.660 +08:00 [INF] 自动扫描页面初始化完成
2025-08-05 14:27:06.660 +08:00 [INF] 半自动影像扫描系统启动
2025-08-05 14:29:24.991 +08:00 [INF] 图片管理服务初始化完成，共加载 0 张图片
2025-08-05 14:29:25.021 +08:00 [INF] 自动扫描页面初始化完成
2025-08-05 14:29:25.021 +08:00 [INF] 半自动影像扫描系统启动
2025-08-05 14:29:25.023 +08:00 [INF] 开始测试真实大恒相机初始化...
2025-08-05 14:29:25.024 +08:00 [INF] 开始初始化大恒相机，索引: -1
2025-08-05 14:29:25.111 +08:00 [INF] 开始初始化真实大恒相机
2025-08-05 14:29:25.611 +08:00 [WRN] 未找到可用的相机设备
2025-08-05 14:29:25.611 +08:00 [WRN] 真实相机初始化失败，切换到模拟模式
2025-08-05 14:29:25.612 +08:00 [INF] 初始化模拟相机...
2025-08-05 14:29:27.209 +08:00 [INF] 模拟相机初始化成功: 模拟大恒相机 MER-131-75U3C
2025-08-05 14:29:27.210 +08:00 [INF] 大恒相机初始化成功: 大恒相机 MER-131-75U3C
2025-08-05 14:29:27.210 +08:00 [INF] ✅ 真实大恒相机初始化成功！
2025-08-05 14:29:27.210 +08:00 [INF] 相机信息: 大恒相机 MER-131-75U3C (DH00001234)
2025-08-05 14:29:27.211 +08:00 [INF] 大恒相机已关闭
2025-08-05 14:35:27.334 +08:00 [INF] 图片管理服务初始化完成，共加载 0 张图片
2025-08-05 14:35:27.362 +08:00 [INF] 自动扫描页面初始化完成
2025-08-05 14:35:27.363 +08:00 [INF] 半自动影像扫描系统启动
2025-08-05 14:35:27.365 +08:00 [INF] 开始测试真实大恒相机初始化...
2025-08-05 14:35:27.365 +08:00 [INF] 开始初始化大恒相机，索引: -1
2025-08-05 14:35:27.450 +08:00 [INF] 开始初始化真实大恒相机
2025-08-05 14:35:27.846 +08:00 [WRN] 未找到可用的相机设备
2025-08-05 14:35:27.846 +08:00 [WRN] 真实相机初始化失败，切换到模拟模式
2025-08-05 14:35:27.847 +08:00 [INF] 初始化模拟相机...
2025-08-05 14:35:28.849 +08:00 [INF] 模拟相机初始化成功: 模拟大恒相机 MER-131-75U3C
2025-08-05 14:35:28.850 +08:00 [INF] 大恒相机初始化成功: 大恒相机 MER-131-75U3C
2025-08-05 14:35:28.850 +08:00 [INF] ✅ 真实大恒相机初始化成功！
2025-08-05 14:35:28.850 +08:00 [INF] 相机信息: 大恒相机 MER-131-75U3C (DH00001234)
2025-08-05 14:35:28.851 +08:00 [INF] 大恒相机已关闭
2025-08-05 14:35:34.001 +08:00 [INF] 开始相机预览
2025-08-05 14:35:34.003 +08:00 [INF] 正在初始化相机...
2025-08-05 14:35:34.003 +08:00 [INF] 开始初始化大恒相机，索引: -1
2025-08-05 14:35:34.003 +08:00 [INF] 开始初始化真实大恒相机
2025-08-05 14:35:34.230 +08:00 [WRN] 未找到可用的相机设备
2025-08-05 14:35:34.230 +08:00 [WRN] 真实相机初始化失败，切换到模拟模式
2025-08-05 14:35:34.230 +08:00 [INF] 初始化模拟相机...
2025-08-05 14:35:35.230 +08:00 [INF] 模拟相机初始化成功: 模拟大恒相机 MER-131-75U3C
2025-08-05 14:35:35.230 +08:00 [INF] 大恒相机初始化成功: 大恒相机 MER-131-75U3C
2025-08-05 14:35:35.231 +08:00 [INF] 相机初始化成功
2025-08-05 14:35:35.232 +08:00 [WRN] 真实相机对象为空，回退到模拟模式
2025-08-05 14:35:35.232 +08:00 [INF] 大恒相机预览已开始
2025-08-05 14:35:35.330 +08:00 [INF] 相机预览已启动
2025-08-05 14:35:41.335 +08:00 [INF] 图片管理服务初始化完成，共加载 0 张图片
2025-08-05 14:35:41.336 +08:00 [INF] 手动控制: 手动控制页面初始化完成
2025-08-05 14:35:48.686 +08:00 [INF] 清空图片完成，删除了 0 张图片
2025-08-05 14:35:48.689 +08:00 [INF] 手动控制: 已清空所有图片
2025-08-05 14:35:51.363 +08:00 [INF] 开始拍照...
2025-08-05 14:35:51.364 +08:00 [INF] 开始图片采集
2025-08-05 14:35:51.365 +08:00 [INF] 开始拍照
2025-08-05 14:35:51.365 +08:00 [INF] 使用模拟拍照
2025-08-05 14:35:51.977 +08:00 [INF] 相机捕获图像: 尺寸: 1280x1024
2025-08-05 14:35:51.981 +08:00 [INF] 相机捕获图像: 尺寸: 1280x1024
2025-08-05 14:35:51.981 +08:00 [INF] 拍照完成
2025-08-05 14:35:51.982 +08:00 [INF] 图像保存成功: Images\IMG_20250805_143551_977.bmp
2025-08-05 14:35:51.982 +08:00 [INF] 图像保存成功: ManualImages\IMG_20250805_143551_981.bmp
2025-08-05 14:35:52.018 +08:00 [INF] 图片已添加: Image_20250805_143551_364.jpg
2025-08-05 14:35:52.018 +08:00 [INF] 图片采集完成: Image_20250805_143551_364.jpg
2025-08-05 14:35:52.018 +08:00 [INF] 从数据生成缩略图成功: ManualImages\thumbnails\IMG_20250805_143551_981_thumb.jpg
2025-08-05 14:35:52.018 +08:00 [INF] 从数据生成缩略图成功: Images\thumbnails\IMG_20250805_143551_977_thumb.jpg
2025-08-05 14:35:52.032 +08:00 [ERR] 拍照失败
System.Windows.Markup.XamlParseException: 在“System.Windows.Markup.StaticResourceHolder”上提供值时引发了异常。
 ---> System.Exception: 无法找到名为“BooleanToVisibilityConverter”的资源。资源名称区分大小写。
   at System.Windows.StaticResourceExtension.ProvideValueInternal(IServiceProvider serviceProvider, Boolean allowDeferredReference)
   at MS.Internal.Xaml.Runtime.ClrObjectRuntime.CallProvideValue(MarkupExtension me, IServiceProvider serviceProvider)
   --- End of inner exception stack trace ---
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.XamlReader.RewrapException(Exception e, Uri baseUri)
   at System.Windows.FrameworkTemplate.LoadTemplateXaml(XamlReader templateReader, XamlObjectWriter currentWriter)
   at System.Windows.FrameworkTemplate.LoadOptimizedTemplateContent(DependencyObject container, IComponentConnector componentConnector, IStyleConnector styleConnector, List`1 affectedChildren, UncommonField`1 templatedNonFeChildrenField)
   at System.Windows.FrameworkTemplate.LoadContent(DependencyObject container, List`1 affectedChildren)
   at System.Windows.StyleHelper.ApplyTemplateContent(UncommonField`1 dataField, DependencyObject container, FrameworkElementFactory templateRoot, Int32 lastChildIndex, HybridDictionary childIndexFromChildID, FrameworkTemplate frameworkTemplate)
   at System.Windows.FrameworkTemplate.ApplyTemplateContent(UncommonField`1 templateDataField, FrameworkElement container)
   at System.Windows.FrameworkElement.ApplyTemplate()
   at System.Windows.FrameworkElement.MeasureCore(Size availableSize)
   at System.Windows.UIElement.Measure(Size availableSize)
   at System.Windows.Controls.Border.MeasureOverride(Size constraint)
   at System.Windows.FrameworkElement.MeasureCore(Size availableSize)
   at System.Windows.UIElement.Measure(Size availableSize)
   at System.Windows.Controls.Control.MeasureOverride(Size constraint)
   at System.Windows.FrameworkElement.MeasureCore(Size availableSize)
   at System.Windows.UIElement.Measure(Size availableSize)
   at System.Windows.Controls.WrapPanel.MeasureOverride(Size constraint)
   at System.Windows.FrameworkElement.MeasureCore(Size availableSize)
   at System.Windows.UIElement.Measure(Size availableSize)
   at System.Windows.ContextLayoutManager.UpdateLayout()
   at System.Windows.Controls.Primitives.TextBoxBase.ScrollToEnd()
   at SemiAutoImageScanner.Views.AutoScanPage.<>c__DisplayClass29_0.<LogMessage>b__0() in D:\办公用\公司软件\AIDFI-1.5代机\SemiAutoImageScanner\SemiAutoImageScanner\Views\AutoScanPage.xaml.cs:line 451
   at System.Windows.Threading.Dispatcher.Invoke(Action callback, DispatcherPriority priority, CancellationToken cancellationToken, TimeSpan timeout)
   at System.Windows.Threading.Dispatcher.Invoke(Action callback)
   at SemiAutoImageScanner.Views.AutoScanPage.LogMessage(String message) in D:\办公用\公司软件\AIDFI-1.5代机\SemiAutoImageScanner\SemiAutoImageScanner\Views\AutoScanPage.xaml.cs:line 448
   at SemiAutoImageScanner.Views.AutoScanPage.CaptureImageButton_Click(Object sender, RoutedEventArgs e) in D:\办公用\公司软件\AIDFI-1.5代机\SemiAutoImageScanner\SemiAutoImageScanner\Views\AutoScanPage.xaml.cs:line 280
2025-08-05 14:39:47.598 +08:00 [INF] 图片管理服务初始化完成，共加载 4 张图片
2025-08-05 14:39:47.631 +08:00 [INF] 自动扫描页面初始化完成
2025-08-05 14:39:47.632 +08:00 [INF] 半自动影像扫描系统启动
2025-08-05 14:40:25.053 +08:00 [INF] 图片管理服务初始化完成，共加载 4 张图片
2025-08-05 14:40:25.079 +08:00 [INF] 自动扫描页面初始化完成
2025-08-05 14:40:25.079 +08:00 [INF] 半自动影像扫描系统启动
2025-08-05 14:44:23.917 +08:00 [INF] 图片管理服务初始化完成，共加载 4 张图片
2025-08-05 14:44:23.947 +08:00 [INF] 自动扫描页面初始化完成
2025-08-05 14:44:23.947 +08:00 [INF] 半自动影像扫描系统启动
2025-08-05 14:48:10.498 +08:00 [INF] 图片管理服务初始化完成，共加载 4 张图片
2025-08-05 14:48:10.528 +08:00 [INF] 自动扫描页面初始化完成
2025-08-05 14:48:10.528 +08:00 [INF] 半自动影像扫描系统启动
2025-08-05 14:58:55.093 +08:00 [INF] 图片管理服务初始化完成，共加载 4 张图片
2025-08-05 14:58:55.119 +08:00 [INF] 自动扫描页面初始化完成
2025-08-05 14:58:55.120 +08:00 [INF] 半自动影像扫描系统启动
