using System;
using System.Runtime.InteropServices;
using System.Drawing;
using System.Drawing.Imaging;
using System.Windows.Media.Imaging;
using System.Windows.Media;
using System.IO;
using GxIAPINET;

namespace SemiAutoImageScanner.Utils
{
    /// <summary>
    /// 简化的大恒相机图像处理类，适用于WPF环境
    /// </summary>
    public class GxBitmap
    {
        private BitmapSource m_wpfBitmap = null;        ///<WPF位图对象
        private byte[] m_processedBuffer = null;        ///<处理后的图像数据
        private int m_nWidth = 0;                       ///<图像宽度
        private int m_nHeight = 0;                      ///<图像高度

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public GxBitmap()
        {
        }

        /// <summary>
        /// 图像处理和改善
        /// </summary>
        /// <param name="objIFrameData">图像数据</param>
        /// <param name="objIGXFeatureControl">特征控制</param>
        /// <param name="bImprovement">是否进行图像改善</param>
        public void ImageImprovement(IFrameData objIFrameData, IGXFeatureControl objIGXFeatureControl, bool bImprovement)
        {
            try
            {
                if (objIFrameData == null || objIFrameData.GetStatus() != GX_FRAME_STATUS_LIST.GX_FRAME_STATUS_SUCCESS)
                {
                    return;
                }

                m_nWidth = (int)objIFrameData.GetWidth();
                m_nHeight = (int)objIFrameData.GetHeight();
                var payloadSize = objIFrameData.GetPayloadSize();

                // 获取原始图像数据
                var rawBuffer = new byte[payloadSize];
                Marshal.Copy(objIFrameData.GetBuffer(), rawBuffer, 0, (int)payloadSize);

                // 根据像素格式处理图像
                var pixelFormat = objIFrameData.GetPixelFormat();
                
                if (IsMonoPixelFormat(pixelFormat))
                {
                    // 单色图像处理
                    ProcessMonoImage(rawBuffer, pixelFormat);
                }
                else if (IsColorPixelFormat(pixelFormat))
                {
                    // 彩色图像处理
                    ProcessColorImage(rawBuffer, pixelFormat, objIGXFeatureControl, bImprovement);
                }
                else
                {
                    // 默认按单色处理
                    ProcessMonoImage(rawBuffer, pixelFormat);
                }
            }
            catch (Exception ex)
            {
                // 简单的错误处理，可以根据需要添加日志
                System.Diagnostics.Debug.WriteLine($"图像处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取处理后的WPF位图
        /// </summary>
        /// <returns>BitmapSource对象</returns>
        public BitmapSource GetBitmap()
        {
            return m_wpfBitmap;
        }

        /// <summary>
        /// 判断是否为单色像素格式
        /// </summary>
        private bool IsMonoPixelFormat(GX_PIXEL_FORMAT_ENTRY pixelFormat)
        {
            return pixelFormat == GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_MONO8 ||
                   pixelFormat == GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_MONO10 ||
                   pixelFormat == GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_MONO12 ||
                   pixelFormat == GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_MONO16;
        }

        /// <summary>
        /// 判断是否为彩色像素格式
        /// </summary>
        private bool IsColorPixelFormat(GX_PIXEL_FORMAT_ENTRY pixelFormat)
        {
            return pixelFormat == GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_RGB8 ||
                   pixelFormat == GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_BGR8 ||
                   pixelFormat == GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_BAYER_GR8 ||
                   pixelFormat == GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_BAYER_RG8 ||
                   pixelFormat == GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_BAYER_GB8 ||
                   pixelFormat == GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_BAYER_BG8;
        }

        /// <summary>
        /// 处理单色图像
        /// </summary>
        private void ProcessMonoImage(byte[] rawBuffer, GX_PIXEL_FORMAT_ENTRY pixelFormat)
        {
            try
            {
                // 对于Mono8格式，直接使用原始数据
                if (pixelFormat == GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_MONO8)
                {
                    m_processedBuffer = rawBuffer;
                }
                else
                {
                    // 对于其他单色格式，转换为8位
                    m_processedBuffer = ConvertToMono8(rawBuffer, pixelFormat);
                }

                // 创建WPF位图
                var stride = m_nWidth;
                m_wpfBitmap = BitmapSource.Create(m_nWidth, m_nHeight, 96, 96, 
                    PixelFormats.Gray8, null, m_processedBuffer, stride);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"单色图像处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理彩色图像
        /// </summary>
        private void ProcessColorImage(byte[] rawBuffer, GX_PIXEL_FORMAT_ENTRY pixelFormat, 
            IGXFeatureControl featureControl, bool bImprovement)
        {
            try
            {
                // 简化的彩色图像处理
                if (pixelFormat == GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_RGB8)
                {
                    m_processedBuffer = rawBuffer;
                    var stride = m_nWidth * 3;
                    m_wpfBitmap = BitmapSource.Create(m_nWidth, m_nHeight, 96, 96, 
                        PixelFormats.Rgb24, null, m_processedBuffer, stride);
                }
                else if (pixelFormat == GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_BGR8)
                {
                    m_processedBuffer = rawBuffer;
                    var stride = m_nWidth * 3;
                    m_wpfBitmap = BitmapSource.Create(m_nWidth, m_nHeight, 96, 96, 
                        PixelFormats.Bgr24, null, m_processedBuffer, stride);
                }
                else
                {
                    // 对于Bayer格式，简化处理为灰度图
                    m_processedBuffer = ConvertBayerToMono8(rawBuffer);
                    var stride = m_nWidth;
                    m_wpfBitmap = BitmapSource.Create(m_nWidth, m_nHeight, 96, 96, 
                        PixelFormats.Gray8, null, m_processedBuffer, stride);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"彩色图像处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 转换为Mono8格式
        /// </summary>
        private byte[] ConvertToMono8(byte[] rawBuffer, GX_PIXEL_FORMAT_ENTRY pixelFormat)
        {
            var mono8Buffer = new byte[m_nWidth * m_nHeight];
            
            if (pixelFormat == GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_MONO10 ||
                pixelFormat == GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_MONO12 ||
                pixelFormat == GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_MONO16)
            {
                // 简化的位深度转换：取高8位
                for (int i = 0; i < mono8Buffer.Length; i++)
                {
                    if (i * 2 + 1 < rawBuffer.Length)
                    {
                        mono8Buffer[i] = rawBuffer[i * 2 + 1]; // 取高字节
                    }
                }
            }
            else
            {
                // 默认直接复制
                Array.Copy(rawBuffer, mono8Buffer, Math.Min(rawBuffer.Length, mono8Buffer.Length));
            }
            
            return mono8Buffer;
        }

        /// <summary>
        /// 简化的Bayer转灰度处理
        /// </summary>
        private byte[] ConvertBayerToMono8(byte[] rawBuffer)
        {
            var mono8Buffer = new byte[m_nWidth * m_nHeight];
            
            // 简化处理：直接使用原始数据作为灰度值
            Array.Copy(rawBuffer, mono8Buffer, Math.Min(rawBuffer.Length, mono8Buffer.Length));
            
            return mono8Buffer;
        }
    }
}
